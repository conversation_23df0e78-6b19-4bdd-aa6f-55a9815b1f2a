
import React, { useState, useCallback, useEffect } from 'react';
import { Plant, GeminiDiagnosis } from '@/types';
import { useAuth } from '@/hooks/useAuth';
import { uploadImage, analyzePlantImages, addDiagnosticRecord } from '@/services/api';
import { Button } from '@/components/common/Button';
import { Spinner } from '@/components/common/Spinner';
import { CameraIcon, XMarkIcon } from '@/components/common/icons';
import { motion, AnimatePresence } from 'framer-motion';
import { serverTimestamp, Timestamp } from 'firebase/firestore';
import imageCompression from 'browser-image-compression';
import { GooglePhotosService, GooglePhoto } from '@/services/googlePhotosService';

interface NewDiagnosticProps {
  plant: Plant;
  onFinish: () => void;
}

type Stage = 'upload' | 'analyzing' | 'result';
type UploadTab = 'local' | 'google-photos';

export const NewDiagnostic: React.FC<NewDiagnosticProps> = ({ plant, onFinish }) => {
  const { user } = useAuth();
  const [stage, setStage] = useState<Stage>('upload');
  const [activeTab, setActiveTab] = useState<UploadTab>('local');
  const [files, setFiles] = useState<File[]>([]);
  const [previews, setPreviews] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<GeminiDiagnosis | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);

  // États pour Google Photos
  const [googlePhotos, setGooglePhotos] = useState<GooglePhoto[]>([]);
  const [loadingGooglePhotos, setLoadingGooglePhotos] = useState(false);
  const [hasPhotosPermission, setHasPhotosPermission] = useState(false);
  const [selectedGooglePhotos, setSelectedGooglePhotos] = useState<Set<string>>(new Set());

  const processFiles = async (fileList: FileList | File[]) => {
    const selectedFiles = Array.from(fileList);

    const options = {
        maxSizeMB: 1,
        maxWidthOrHeight: 1920,
        useWebWorker: true
    }

    try {
        const compressedFiles = await Promise.all(selectedFiles.map(file => imageCompression(file, options)));
        setFiles(prev => [...prev, ...compressedFiles]);

        const newPreviews = compressedFiles.map(file => URL.createObjectURL(file));
        setPreviews(prev => [...prev, ...newPreviews]);

    } catch (error) {
        console.error(error);
        setError("Échec de la compression des images. Veuillez réessayer.");
    }
  };

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
        await processFiles(event.target.files);
    }
  };

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback(async (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);

    const droppedFiles = Array.from(e.dataTransfer.files).filter(file =>
      file.type.startsWith('image/')
    );

    if (droppedFiles.length > 0) {
      await processFiles(droppedFiles);
    } else {
      setError("Veuillez déposer uniquement des fichiers image.");
    }
  }, []);

  const removeImage = (index: number) => {
    setFiles(files.filter((_, i) => i !== index));
    setPreviews(previews.filter((_, i) => i !== index));
  };

  // Vérifier les permissions Google Photos au chargement
  useEffect(() => {
    const checkPhotosPermission = async () => {
      try {
        const hasPermission = await GooglePhotosService.hasPhotosPermission();
        setHasPhotosPermission(hasPermission);
      } catch (error) {
        console.error('Erreur lors de la vérification des permissions:', error);
      }
    };

    if (user) {
      checkPhotosPermission();
    }
  }, [user]);

  // Charger les photos Google Photos
  const loadGooglePhotos = async () => {
    if (!hasPhotosPermission) {
      setError('Permissions Google Photos non accordées. Veuillez vous reconnecter.');
      return;
    }

    setLoadingGooglePhotos(true);
    setError(null);

    try {
      const photos = await GooglePhotosService.getRecentPhotos();
      setGooglePhotos(photos);
    } catch (error) {
      console.error('Erreur lors du chargement des photos Google:', error);
      setError('Impossible de charger les photos Google Photos. Veuillez réessayer.');
    } finally {
      setLoadingGooglePhotos(false);
    }
  };

  // Basculer la sélection d'une photo Google
  const toggleGooglePhotoSelection = (photoId: string) => {
    setSelectedGooglePhotos(prev => {
      const newSelection = new Set(prev);
      if (newSelection.has(photoId)) {
        newSelection.delete(photoId);
      } else {
        newSelection.add(photoId);
      }
      return newSelection;
    });
  };

  // Importer les photos sélectionnées depuis Google Photos
  const importSelectedGooglePhotos = async () => {
    if (selectedGooglePhotos.size === 0) {
      setError('Veuillez sélectionner au moins une photo.');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const selectedPhotos = googlePhotos.filter(photo =>
        selectedGooglePhotos.has(photo.id)
      );

      const downloadedFiles = await Promise.all(
        selectedPhotos.map(photo => GooglePhotosService.downloadPhotoAsFile(photo))
      );

      // Compresser les fichiers téléchargés
      const options = {
        maxSizeMB: 1,
        maxWidthOrHeight: 1920,
        useWebWorker: true
      };

      const compressedFiles = await Promise.all(
        downloadedFiles.map(file => imageCompression(file, options))
      );

      // Ajouter aux fichiers existants
      setFiles(prev => [...prev, ...compressedFiles]);

      // Créer les aperçus
      const newPreviews = compressedFiles.map(file => URL.createObjectURL(file));
      setPreviews(prev => [...prev, ...newPreviews]);

      // Réinitialiser la sélection et basculer vers l'onglet local
      setSelectedGooglePhotos(new Set());
      setActiveTab('local');

    } catch (error) {
      console.error('Erreur lors de l\'importation des photos Google:', error);
      setError('Erreur lors de l\'importation des photos. Veuillez réessayer.');
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleAnalyze = async () => {
    if (!user || files.length === 0) return;

    setIsLoading(true);
    setStage('analyzing');
    setError(null);

    try {
      const imageUrls = await Promise.all(
        files.map(file => uploadImage(file, `diagnostics/${user.uid}/${plant.id}/${Date.now()}_${file.name}`))
      );

      const base64Images = await Promise.all(
          files.map(file => imageCompression.getDataUrlFromFile(file).then(url => url.split(',')[1]))
      );

      const analysisResult = await analyzePlantImages(base64Images, plant.name);
      
      setResult(analysisResult);
      setStage('result');

    } catch (err) {
      console.error(err);
      setError('Une erreur s\'est produite pendant l\'analyse. Veuillez réessayer.');
      setStage('upload');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveResult = async () => {
      if(!user || !plant || !result) return;
      setIsLoading(true);
      try {
        const imageUrls = await Promise.all(
            files.map(file => uploadImage(file, `diagnostics/${user.uid}/${plant.id}/${Date.now()}_${file.name}`))
        );

        const newRecord = {
            userId: user.uid,
            plantId: plant.id,
            timestamp: serverTimestamp(),
            imageUrls,
            diagnosis: result,
        } as any;

        if(result.treatmentPlan.treatmentFrequencyDays > 0) {
            const nextDate = new Date();
            nextDate.setDate(nextDate.getDate() + result.treatmentPlan.treatmentFrequencyDays);
            newRecord.nextTreatmentDate = Timestamp.fromDate(nextDate);
        }

        await addDiagnosticRecord(user.uid, plant.id, newRecord);
        onFinish();

      } catch (error) {
          console.error("Failed to save result", error);
          setError("Impossible de sauvegarder l'enregistrement du diagnostic.");
      } finally {
          setIsLoading(false);
      }
  };

  const renderContent = () => {
    switch (stage) {
      case 'upload':
        return (
          <>
            <h2 className="text-3xl font-bold text-white mb-2">Nouveau Diagnostic pour {plant.name}</h2>
            <p className="text-[#E0E0E0] mb-6">Téléchargez des photos de votre plante. Pour de meilleurs résultats, incluez une image claire de la zone affectée.</p>

            {/* Onglets de sélection */}
            <div className="flex mb-6 bg-[#100f1c] rounded-lg p-1">
              <button
                onClick={() => setActiveTab('local')}
                className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-all duration-200 ${
                  activeTab === 'local'
                    ? 'bg-[#d385f5] text-white'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                📁 Fichiers Locaux
              </button>
              <button
                onClick={() => setActiveTab('google-photos')}
                className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-all duration-200 ${
                  activeTab === 'google-photos'
                    ? 'bg-[#d385f5] text-white'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                📸 Google Photos
              </button>
            </div>

            {/* Contenu de l'onglet actif */}
            {activeTab === 'local' ? (
              <div
                className={`p-8 border-2 border-dashed rounded-2xl text-center transition-all duration-200 ${
                  isDragOver
                    ? 'border-[#d385f5] bg-[#d385f5]/10'
                    : 'border-gray-600 bg-[#100f1c]'
                }`}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
              >
                <input type="file" id="file-upload" className="hidden" multiple accept="image/*" onChange={handleFileChange} />
                <label htmlFor="file-upload" className="cursor-pointer">
                  <CameraIcon className={`w-12 h-12 mx-auto mb-4 transition-colors duration-200 ${
                    isDragOver ? 'text-[#d385f5]' : 'text-gray-400'
                  }`} />
                  <p className={`font-semibold transition-colors duration-200 ${
                    isDragOver ? 'text-[#d385f5]' : 'text-white'
                  }`}>
                    {isDragOver ? 'Déposez vos images ici' : 'Cliquez pour télécharger des photos'}
                  </p>
                  <p className={`text-sm transition-colors duration-200 ${
                    isDragOver ? 'text-[#d385f5]/80' : 'text-gray-400'
                  }`}>
                    ou glissez-déposez vos images
                  </p>
                </label>
              </div>
            ) : (
              <div className="p-8 border-2 border-gray-600 rounded-2xl bg-[#100f1c]">
                {!hasPhotosPermission ? (
                  <div className="text-center">
                    <p className="text-gray-400 mb-4">
                      Accès aux Google Photos non autorisé. Veuillez vous reconnecter pour accorder les permissions.
                    </p>
                    <Button variant="secondary" onClick={() => window.location.reload()}>
                      Se reconnecter
                    </Button>
                  </div>
                ) : (
                  <>
                    <div className="text-center mb-6">
                      <p className="text-white font-semibold mb-2">Photos récentes (dernières 24h)</p>
                      <p className="text-gray-400 text-sm mb-4">
                        Sélectionnez les photos de votre plante prises récemment
                      </p>
                      <Button
                        onClick={loadGooglePhotos}
                        disabled={loadingGooglePhotos}
                        isLoading={loadingGooglePhotos}
                      >
                        {googlePhotos.length > 0 ? 'Actualiser' : 'Charger mes photos'}
                      </Button>
                    </div>

                    {googlePhotos.length > 0 && (
                      <>
                        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 mb-6">
                          {googlePhotos.map((photo) => (
                            <div
                              key={photo.id}
                              className={`relative cursor-pointer rounded-lg overflow-hidden transition-all duration-200 ${
                                selectedGooglePhotos.has(photo.id)
                                  ? 'ring-2 ring-[#d385f5] ring-offset-2 ring-offset-[#0a0a0a]'
                                  : 'hover:ring-1 hover:ring-gray-400'
                              }`}
                              onClick={() => toggleGooglePhotoSelection(photo.id)}
                            >
                              <img
                                src={`${photo.baseUrl}=w300-h300-c`}
                                alt={photo.filename}
                                className="w-full h-32 object-cover"
                              />
                              {selectedGooglePhotos.has(photo.id) && (
                                <div className="absolute top-2 right-2 bg-[#d385f5] rounded-full p-1">
                                  <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                  </svg>
                                </div>
                              )}
                              <div className="absolute bottom-0 left-0 right-0 bg-black/50 text-white text-xs p-2">
                                {new Date(photo.mediaMetadata.creationTime).toLocaleDateString('fr-FR', {
                                  day: '2-digit',
                                  month: '2-digit',
                                  hour: '2-digit',
                                  minute: '2-digit'
                                })}
                              </div>
                            </div>
                          ))}
                        </div>

                        {selectedGooglePhotos.size > 0 && (
                          <div className="text-center">
                            <Button
                              onClick={importSelectedGooglePhotos}
                              disabled={isLoading}
                              isLoading={isLoading}
                            >
                              Importer {selectedGooglePhotos.size} photo(s) sélectionnée(s)
                            </Button>
                          </div>
                        )}
                      </>
                    )}
                  </>
                )}
              </div>
            )}

            {/* Aperçu des images sélectionnées */}
            <AnimatePresence>
                {previews.length > 0 && (
                    <div className="mt-6">
                      <h3 className="text-white font-semibold mb-4">Images sélectionnées ({files.length})</h3>
                      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                        {previews.map((src, index) => (
                            <motion.div key={index} layout className="relative">
                                <img src={src} alt={`preview ${index}`} className="rounded-lg w-full h-32 object-cover"/>
                                <button onClick={() => removeImage(index)} className="absolute top-1 right-1 bg-black/50 rounded-full p-1 text-white">
                                    <XMarkIcon className="w-4 h-4" />
                                </button>
                            </motion.div>
                        ))}
                      </div>
                    </div>
                )}
            </AnimatePresence>

            <div className="mt-8 flex justify-end gap-4">
              <Button variant="secondary" onClick={onFinish}>Annuler</Button>
              <Button onClick={handleAnalyze} disabled={files.length === 0} isLoading={isLoading}>Analyser les Images</Button>
            </div>
          </>
        );
      case 'analyzing':
        return (
            <div className="text-center">
                <Spinner size="lg" />
                <h2 className="text-3xl font-bold text-white mt-6">Analyse en cours...</h2>
                <p className="text-[#E0E0E0] mt-2">FloraSynth inspecte votre plante. Cela peut prendre un moment.</p>
            </div>
        );
      case 'result':
        return result && (
            <div>
                <h2 className="text-3xl font-bold text-white mb-2">Analyse Terminée</h2>
                <p className="text-xl text-white mb-4">Diagnostic : <span className="font-bold bg-clip-text text-transparent bg-gradient-to-r from-[#d385f5] to-[#a364f7]">{result.disease}</span></p>
                <div className="space-y-6 text-[#E0E0E0] p-6 bg-[#100f1c] rounded-lg">
                    <p>{result.description}</p>

                    <div>
                        <h3 className="font-bold text-white text-xl mb-4 flex items-center gap-2">
                            📋 Plan de Traitement
                        </h3>
                        <div className="space-y-3 mb-6">
                            {result.treatmentPlan.steps.map((step, i) => (
                                <div key={i} className="flex items-start gap-3 p-4 bg-[#2a2847] rounded-lg border border-[#3D3B5E]">
                                    <div className="flex-shrink-0 w-7 h-7 bg-[#d385f5] text-white rounded-full flex items-center justify-center text-sm font-bold">
                                        {i + 1}
                                    </div>
                                    <p className="text-[#E0E0E0] leading-relaxed">{step}</p>
                                </div>
                            ))}
                        </div>
                        {result.treatmentPlan.treatmentFrequencyDays > 0 && (
                            <div className="bg-gradient-to-r from-[#d385f5]/20 to-[#a364f7]/20 p-4 rounded-lg border border-[#d385f5]/30 mb-6">
                                <div className="flex items-center gap-2">
                                    <span className="text-2xl">🔄</span>
                                    <p className="font-semibold text-[#d385f5] text-lg">
                                        Répéter le traitement tous les {result.treatmentPlan.treatmentFrequencyDays} jours
                                    </p>
                                </div>
                            </div>
                        )}
                    </div>

                    {result.treatmentPlan.recommendedProducts.length > 0 && (
                        <div>
                            <h3 className="font-bold text-white text-xl mb-4 flex items-center gap-2">
                                🧪 Produits Recommandés et Dosages
                            </h3>
                            <div className="space-y-6">
                                {result.treatmentPlan.recommendedProducts.map((product, i) => (
                                    <div key={i} className="bg-[#2a2847] p-6 rounded-xl border border-[#3D3B5E] shadow-lg">
                                        <div className="flex items-center gap-3 mb-4">
                                            <div className="w-10 h-10 bg-gradient-to-br from-[#d385f5] to-[#a364f7] rounded-full flex items-center justify-center">
                                                <span className="text-white font-bold text-lg">🧪</span>
                                            </div>
                                            <div>
                                                <h4 className="font-bold text-[#d385f5] text-xl">{product.name}</h4>
                                                <p className="text-sm text-[#9CA3AF]">{product.type}</p>
                                            </div>
                                        </div>

                                        {product.dosages && (
                                            <div className="mb-6">
                                                <h5 className="font-bold text-white text-lg mb-4 flex items-center gap-2">
                                                    ⚖️ Dosages par Récipient
                                                </h5>
                                                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                                                    {/* Pulvérisateurs */}
                                                    <div className="space-y-3">
                                                        <h6 className="font-semibold text-[#d385f5] text-sm uppercase tracking-wide flex items-center gap-2">
                                                            💨 Pulvérisateurs
                                                        </h6>
                                                        <div className="space-y-2">
                                                            <div className="flex items-center justify-between p-3 bg-[#1c1a31] rounded-lg border border-[#3D3B5E]">
                                                                <div className="flex items-center gap-2">
                                                                    <span className="text-2xl">🔫</span>
                                                                    <span className="text-[#E0E0E0] font-medium">1L</span>
                                                                </div>
                                                                <span className="font-bold text-[#d385f5] text-lg">{product.dosages.pulverisateur_1L}</span>
                                                            </div>
                                                            <div className="flex items-center justify-between p-3 bg-[#1c1a31] rounded-lg border border-[#3D3B5E]">
                                                                <div className="flex items-center gap-2">
                                                                    <span className="text-2xl">🔫</span>
                                                                    <span className="text-[#E0E0E0] font-medium">5L</span>
                                                                </div>
                                                                <span className="font-bold text-[#d385f5] text-lg">{product.dosages.pulverisateur_5L}</span>
                                                            </div>
                                                            <div className="flex items-center justify-between p-3 bg-[#1c1a31] rounded-lg border border-[#3D3B5E]">
                                                                <div className="flex items-center gap-2">
                                                                    <span className="text-2xl">🔫</span>
                                                                    <span className="text-[#E0E0E0] font-medium">16L</span>
                                                                </div>
                                                                <span className="font-bold text-[#d385f5] text-lg">{product.dosages.pulverisateur_16L}</span>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    {/* Arrosoirs */}
                                                    <div className="space-y-3">
                                                        <h6 className="font-semibold text-[#10B981] text-sm uppercase tracking-wide flex items-center gap-2">
                                                            🪣 Arrosoirs
                                                        </h6>
                                                        <div className="space-y-2">
                                                            <div className="flex items-center justify-between p-3 bg-[#1c1a31] rounded-lg border border-[#3D3B5E]">
                                                                <div className="flex items-center gap-2">
                                                                    <span className="text-2xl">🪣</span>
                                                                    <span className="text-[#E0E0E0] font-medium">11L</span>
                                                                </div>
                                                                <span className="font-bold text-[#10B981] text-lg">{product.dosages.arrosoir_11L}</span>
                                                            </div>
                                                            <div className="flex items-center justify-between p-3 bg-[#1c1a31] rounded-lg border border-[#3D3B5E]">
                                                                <div className="flex items-center gap-2">
                                                                    <span className="text-2xl">🪣</span>
                                                                    <span className="text-[#E0E0E0] font-medium">13L</span>
                                                                </div>
                                                                <span className="font-bold text-[#10B981] text-lg">{product.dosages.arrosoir_13L}</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        )}

                                        <div className="mb-4">
                                            <h5 className="font-bold text-white text-lg mb-3 flex items-center gap-2">
                                                🎯 Méthode d'Application
                                            </h5>
                                            <div className="p-4 bg-[#1c1a31] rounded-lg border border-[#3D3B5E]">
                                                <p className="text-[#E0E0E0] leading-relaxed">{product.applicationMethod}</p>
                                            </div>
                                        </div>

                                        {product.precautions && (
                                            <div className="p-4 bg-gradient-to-r from-orange-500/20 to-red-500/20 border border-orange-500/30 rounded-lg">
                                                <h5 className="font-bold text-orange-400 text-lg mb-2 flex items-center gap-2">
                                                    ⚠️ Précautions Importantes
                                                </h5>
                                                <p className="text-orange-200 leading-relaxed">{product.precautions}</p>
                                            </div>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}

                    <div>
                        <h3 className="font-bold text-white text-xl mb-4 flex items-center gap-2">
                            💡 Conseils de Soin Généraux
                        </h3>
                        <div className="space-y-3">
                            {result.careTips.map((tip, i) => (
                                <div key={i} className="flex items-start gap-3 p-4 bg-[#2a2847] rounded-lg border border-[#3D3B5E]">
                                    <div className="flex-shrink-0 w-6 h-6 bg-gradient-to-br from-[#10B981] to-[#059669] text-white rounded-full flex items-center justify-center text-sm">
                                        💡
                                    </div>
                                    <p className="text-[#E0E0E0] leading-relaxed">{tip}</p>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
                 <div className="mt-8 flex justify-end gap-4">
                    <Button variant="secondary" onClick={() => setStage('upload')}>Re-télécharger</Button>
                    <Button onClick={handleSaveResult} isLoading={isLoading}>Sauvegarder dans l'Historique</Button>
                </div>
            </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-[#100f1c]/80 backdrop-blur-sm">
      <motion.div 
        initial={{opacity: 0, scale: 0.95}}
        animate={{opacity: 1, scale: 1}}
        className="w-full max-w-3xl bg-[#1c1a31] p-8 rounded-2xl"
      >
        {error && <p className="text-red-400 mb-4 text-center">{error}</p>}
        {renderContent()}
      </motion.div>
    </div>
  );
};
