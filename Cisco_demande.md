
**Toujours vérifier ta mémoire et vérifier ensuite ce fichier. On ne sait jamais, je peux en cours de route te mettre de nouvelles instructions lorsque toi tu travailles.pas de fichier MD à la racine. Le seul fichier MD qui existe, c'est le mien : `cisco_demande.md`** Ça, c'est mon fichier personnel. Tu n'écris rien du tout dedans. Tu ne rédiges absolument rien du tout. C'est mon fichier à moi, celui-là. 



Vous avez mon approbation. 



import React, { useState, useEffect } from 'react';
import { getFunctions, httpsCallable } from 'firebase/functions';
import { initializeApp } from 'firebase/app';

// --- Configuration Firebase ---
// Remplace par ta propre configuration Firebase
const firebaseConfig = {
  apiKey: "YOUR_API_KEY",
  authDomain: "YOUR_AUTH_DOMAIN",
  projectId: "YOUR_PROJECT_ID",
  storageBucket: "YOUR_STORAGE_BUCKET",
  messagingSenderId: "YOUR_MESSAGING_SENDER_ID",
  appId: "YOUR_APP_ID"
};

// --- Configuration API Google ---
// IMPORTANT : Tu dois créer ces identifiants dans la Google Cloud Console
// pour ton projet. Va dans "API et services" > "Identifiants".
const GOOGLE_API_KEY = "YOUR_GOOGLE_API_KEY";
const GOOGLE_CLIENT_ID = "YOUR_GOOGLE_CLIENT_ID";
// Le "scope" définit les permissions que tu demandes à l'utilisateur.
// 'readonly' est le plus sûr car on ne veut que lire les photos.
const SCOPES = "https://www.googleapis.com/auth/photoslibrary.readonly";

// --- Initialisation Firebase ---
const app = initializeApp(firebaseConfig);
const functions = getFunctions(app, 'europe-west1');

export default function PhotoPickerAnalyzer() {
  const [googleAuth, setGoogleAuth] = useState(null);
  const [isSignedIn, setIsSignedIn] = useState(false);
  const [photos, setPhotos] = useState([]);
  const [analysis, setAnalysis] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  // --- Étape 1 : Charger le client API de Google ---
  useEffect(() => {
    const script = document.createElement('script');
    script.src = 'https://apis.google.com/js/api.js';
    script.onload = () => {
      window.gapi.load('client:auth2', initClient);
    };
    document.body.appendChild(script);
  }, []);

  // --- Étape 2 : Initialiser le client d'authentification Google ---
  const initClient = async () => {
    try {
      await window.gapi.client.init({
        apiKey: GOOGLE_API_KEY,
        clientId: GOOGLE_CLIENT_ID,
        scope: SCOPES,
        discoveryDocs: ["https://www.googleapis.com/discovery/v1/apis/photoslibrary/v1/rest"],
      });
      const authInstance = window.gapi.auth2.getAuthInstance();
      setGoogleAuth(authInstance);
      // Vérifier si l'utilisateur est déjà connecté
      const signedIn = authInstance.isSignedIn.get();
      setIsSignedIn(signedIn);
      if (signedIn) {
        listPhotos();
      }
    } catch (e) {
      console.error("Erreur d'initialisation du client Google", e);
      setError("Impossible de charger l'API Google Photos.");
    }
  };

  // --- Étape 3 : Gérer la connexion / déconnexion ---
  const handleAuthClick = () => {
    if (isSignedIn) {
      googleAuth.signOut();
      setIsSignedIn(false);
      setPhotos([]);
    } else {
      googleAuth.signIn().then(() => {
        setIsSignedIn(true);
        listPhotos();
      });
    }
  };

  // --- Étape 4 : Lister les photos de l'utilisateur ---
  const listPhotos = async () => {
    setIsLoading(true);
    setError("");
    try {
      const response = await window.gapi.client.photoslibrary.mediaItems.list({
        pageSize: 20, // Récupère les 20 photos les plus récentes
      });
      setPhotos(response.result.mediaItems || []);
    } catch (err) {
      console.error("Erreur lors de la récupération des photos", err);
      setError("Impossible de charger les photos depuis Google Photos.");
    } finally {
      setIsLoading(false);
    }
  };

  // --- Étape 5 : Analyser la photo sélectionnée ---
  const handlePhotoSelect = async (photo) => {
    if (!photo || !photo.baseUrl) return;
    
    setIsLoading(true);
    setError("");
    setAnalysis("");
    
    // On scrolle vers le haut pour voir le résultat
    window.scrollTo({ top: 0, behavior: 'smooth' });

    try {
      // On appelle la Cloud Function avec l'URL de la photo
      const analyzeImageFunction = httpsCallable(functions, 'analyzeImageUrl');
      // IMPORTANT: La Cloud Function doit être adaptée pour accepter une URL
      const result = await analyzeImageFunction({ imageUrl: photo.baseUrl });
      setAnalysis(result.data.analysis);
    } catch (err) {
      console.error("Erreur lors de l'analyse via la Cloud Function:", err);
      setError("Une erreur est survenue lors de l'analyse de l'image.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-gray-50 min-h-screen p-4 font-sans">
      <div className="w-full max-w-4xl mx-auto bg-white rounded-2xl shadow-lg p-6 md:p-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-green-800">FloraSynth AI</h1>
          <button
            onClick={handleAuthClick}
            className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded-lg transition-all duration-300 shadow-md"
          >
            {isSignedIn ? "Déconnexion" : "Connecter avec Google"}
          </button>
        </div>

        {/* --- Zone de résultat --- */}
        {isLoading && <div className="text-center p-4">Chargement...</div>}
        {error && <div className="text-red-600 bg-red-100 p-3 rounded-lg text-center mb-4">{error}</div>}
        {analysis && (
          <div className="bg-gray-100 p-4 rounded-lg mb-6">
            <h3 className="text-xl font-semibold text-gray-800 mb-2">Résultat de l'analyse :</h3>
            <p className="text-gray-700 whitespace-pre-wrap">{analysis}</p>
          </div>
        )}

        {/* --- Galerie de photos --- */}
        {isSignedIn && (
          <div>
            <h2 className="text-2xl font-semibold text-gray-700 mb-4">Sélectionnez une plante à analyser</h2>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
              {photos.map((photo) => (
                <div key={photo.id} className="cursor-pointer group" onClick={() => handlePhotoSelect(photo)}>
                  <img
                    src={`${photo.baseUrl}=w400-h400-c`} // URL pour une miniature optimisée
                    alt="Photo de plante"
                    className="rounded-lg object-cover w-full h-full aspect-square group-hover:opacity-75 transition-opacity duration-300 shadow-md"
                  />
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}















