import { auth } from './api';

// Interface pour les photos Google Photos
export interface GooglePhoto {
  id: string;
  baseUrl: string;
  filename: string;
  mediaMetadata: {
    creationTime: string;
    width: string;
    height: string;
  };
  mimeType: string;
}

// Interface pour la réponse de l'API Google Photos
interface GooglePhotosResponse {
  mediaItems: GooglePhoto[];
  nextPageToken?: string;
}

// Configuration Google API depuis les variables d'environnement
const GOOGLE_API_KEY = import.meta.env.VITE_GOOGLE_API_KEY;
const GOOGLE_CLIENT_ID = import.meta.env.VITE_GOOGLE_CLIENT_ID;
const SCOPES = import.meta.env.VITE_GOOGLE_PHOTOS_SCOPE || 'https://www.googleapis.com/auth/photoslibrary.readonly';

// Déclaration des types pour l'API Google
declare global {
  interface Window {
    gapi: any;
  }
}

/**
 * Service pour interagir avec l'API Google Photos
 */
export class GooglePhotosService {
  private static readonly BASE_URL = 'https://photoslibrary.googleapis.com/v1';
  private static isInitialized = false;
  private static authInstance: any = null;

  /**
   * Initialiser l'API Google Photos
   */
  static async initialize(): Promise<void> {
    if (this.isInitialized) return;

    return new Promise((resolve, reject) => {
      // Charger l'API Google si pas déjà chargée
      if (!window.gapi) {
        const script = document.createElement('script');
        script.src = 'https://apis.google.com/js/api.js';
        script.onload = () => {
          window.gapi.load('client:auth2', () => this.initClient().then(resolve).catch(reject));
        };
        script.onerror = () => reject(new Error('Impossible de charger l\'API Google'));
        document.body.appendChild(script);
      } else {
        window.gapi.load('client:auth2', () => this.initClient().then(resolve).catch(reject));
      }
    });
  }

  /**
   * Initialiser le client Google API
   */
  private static async initClient(): Promise<void> {
    try {
      await window.gapi.client.init({
        apiKey: GOOGLE_API_KEY,
        clientId: GOOGLE_CLIENT_ID,
        scope: SCOPES,
        discoveryDocs: ['https://www.googleapis.com/discovery/v1/apis/photoslibrary/v1/rest'],
      });

      this.authInstance = window.gapi.auth2.getAuthInstance();
      this.isInitialized = true;
    } catch (error) {
      console.error('Erreur d\'initialisation du client Google:', error);
      throw new Error('Impossible d\'initialiser l\'API Google Photos');
    }
  }

  /**
   * Vérifier si l'utilisateur est connecté à Google Photos
   */
  static async isSignedIn(): Promise<boolean> {
    await this.initialize();
    return this.authInstance?.isSignedIn.get() || false;
  }

  /**
   * Connecter l'utilisateur à Google Photos
   */
  static async signIn(): Promise<void> {
    await this.initialize();

    if (!this.authInstance) {
      throw new Error('Instance d\'authentification non disponible');
    }

    try {
      await this.authInstance.signIn();
    } catch (error) {
      console.error('Erreur lors de la connexion Google Photos:', error);
      throw new Error('Impossible de se connecter à Google Photos');
    }
  }

  /**
   * Obtenir le token d'accès Google OAuth pour les Photos
   */
  private static async getGoogleAccessToken(): Promise<string> {
    await this.initialize();

    if (!this.authInstance?.isSignedIn.get()) {
      await this.signIn();
    }

    const user = this.authInstance.currentUser.get();
    const authResponse = user.getAuthResponse();

    if (!authResponse.access_token) {
      throw new Error('Token d\'accès non disponible');
    }

    return authResponse.access_token;
  }

  /**
   * Récupérer les photos récentes (dernières 24h par défaut)
   */
  static async getRecentPhotos(pageSize: number = 20, hoursBack: number = 24): Promise<GooglePhoto[]> {
    try {
      await this.initialize();

      if (!await this.isSignedIn()) {
        await this.signIn();
      }

      // Calculer la date de début (il y a X heures)
      const startDate = new Date(Date.now() - hoursBack * 60 * 60 * 1000);

      // Utiliser l'API Google Photos pour récupérer les photos
      const response = await window.gapi.client.photoslibrary.mediaItems.list({
        pageSize: pageSize,
        filters: {
          dateFilter: {
            ranges: [{
              startDate: {
                year: startDate.getFullYear(),
                month: startDate.getMonth() + 1,
                day: startDate.getDate()
              },
              endDate: {
                year: new Date().getFullYear(),
                month: new Date().getMonth() + 1,
                day: new Date().getDate()
              }
            }]
          },
          mediaTypeFilter: {
            mediaTypes: ['PHOTO']
          }
        }
      });

      const mediaItems = response.result.mediaItems || [];

      // Convertir au format de notre interface
      const photos: GooglePhoto[] = mediaItems.map((item: any) => ({
        id: item.id,
        baseUrl: item.baseUrl,
        filename: item.filename || `photo_${item.id}.jpg`,
        mediaMetadata: {
          creationTime: item.mediaMetadata.creationTime,
          width: item.mediaMetadata.width || '0',
          height: item.mediaMetadata.height || '0'
        },
        mimeType: item.mimeType || 'image/jpeg'
      }));

      return photos;

    } catch (error) {
      console.error('Erreur lors de la récupération des photos Google:', error);

      // En cas d'erreur, retourner des photos de démonstration avec un message d'avertissement
      console.warn('Utilisation des photos de démonstration en raison d\'une erreur API');
      return this.getDemoPhotos();
    }
  }

  /**
   * Photos de démonstration en cas d'erreur API
   */
  private static getDemoPhotos(): GooglePhoto[] {
    return [
      {
        id: 'demo-1',
        baseUrl: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400',
        filename: 'plante_demo_1.jpg',
        mediaMetadata: {
          creationTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          width: '400',
          height: '300'
        },
        mimeType: 'image/jpeg'
      },
      {
        id: 'demo-2',
        baseUrl: 'https://images.unsplash.com/photo-1485955900006-10f4d324d411?w=400',
        filename: 'plante_demo_2.jpg',
        mediaMetadata: {
          creationTime: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
          width: '400',
          height: '300'
        },
        mimeType: 'image/jpeg'
      },
      {
        id: 'demo-3',
        baseUrl: 'https://images.unsplash.com/photo-1463320726281-696a485928c7?w=400',
        filename: 'plante_demo_3.jpg',
        mediaMetadata: {
          creationTime: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),
          width: '400',
          height: '300'
        },
        mimeType: 'image/jpeg'
      }
    ];
  }



  /**
   * Convertir une photo Google Photos en File pour l'upload
   */
  static async downloadPhotoAsFile(photo: GooglePhoto): Promise<File> {
    try {
      // Pour les vraies photos Google Photos, ajouter les paramètres de taille
      // Pour les photos de démo, utiliser l'URL telle quelle
      const downloadUrl = photo.id.startsWith('demo-')
        ? photo.baseUrl
        : `${photo.baseUrl}=d`; // =d pour télécharger la photo en taille originale

      const response = await fetch(downloadUrl);
      if (!response.ok) {
        throw new Error(`Erreur de téléchargement: ${response.status}`);
      }

      const blob = await response.blob();

      // Créer un objet File à partir du blob
      const file = new File([blob], photo.filename, {
        type: photo.mimeType,
        lastModified: new Date(photo.mediaMetadata.creationTime).getTime()
      });

      return file;

    } catch (error) {
      console.error('Erreur lors du téléchargement de la photo:', error);
      throw new Error('Impossible de télécharger la photo');
    }
  }

  /**
   * Vérifier si l'utilisateur a accordé les permissions Google Photos
   */
  static async hasPhotosPermission(): Promise<boolean> {
    try {
      await this.initialize();

      const user = auth.currentUser;
      if (!user) return false;

      // Vérifier si l'utilisateur s'est connecté avec Google
      const hasGoogleProvider = user.providerData.some(
        provider => provider.providerId === 'google.com'
      );

      if (!hasGoogleProvider) return false;

      // Vérifier si l'utilisateur est connecté à l'API Google Photos
      const isSignedInToPhotos = await this.isSignedIn();

      return isSignedInToPhotos;
    } catch (error) {
      console.error('Erreur lors de la vérification des permissions:', error);
      return false;
    }
  }

  /**
   * Récupérer toutes les photos (sans filtre de date)
   */
  static async getAllPhotos(pageSize: number = 50): Promise<GooglePhoto[]> {
    try {
      await this.initialize();

      if (!await this.isSignedIn()) {
        await this.signIn();
      }

      const response = await window.gapi.client.photoslibrary.mediaItems.list({
        pageSize: pageSize,
        filters: {
          mediaTypeFilter: {
            mediaTypes: ['PHOTO']
          }
        }
      });

      const mediaItems = response.result.mediaItems || [];

      const photos: GooglePhoto[] = mediaItems.map((item: any) => ({
        id: item.id,
        baseUrl: item.baseUrl,
        filename: item.filename || `photo_${item.id}.jpg`,
        mediaMetadata: {
          creationTime: item.mediaMetadata.creationTime,
          width: item.mediaMetadata.width || '0',
          height: item.mediaMetadata.height || '0'
        },
        mimeType: item.mimeType || 'image/jpeg'
      }));

      return photos;

    } catch (error) {
      console.error('Erreur lors de la récupération de toutes les photos:', error);
      return this.getDemoPhotos();
    }
  }

  /**
   * Récupérer les photos par type (plantes, jardinage, etc.)
   */
  static async getPhotosByType(searchTerms: string[] = ['plant', 'garden', 'flower'], pageSize: number = 30): Promise<GooglePhoto[]> {
    try {
      await this.initialize();

      if (!await this.isSignedIn()) {
        await this.signIn();
      }

      // Note: L'API Google Photos ne permet pas de recherche par contenu directement
      // On récupère toutes les photos et on peut filtrer côté client si nécessaire
      const allPhotos = await this.getAllPhotos(pageSize);

      // Pour l'instant, on retourne toutes les photos
      // Dans une version future, on pourrait utiliser l'API Vision pour filtrer
      return allPhotos;

    } catch (error) {
      console.error('Erreur lors de la récupération des photos par type:', error);
      return this.getDemoPhotos();
    }
  }
}
