// Guide de soin des plantes et identification des carences
// Basé sur la base de connaissances FloraSynth

export interface DeficiencyGuide {
  element: string;
  symptoms: string[];
  causes: string[];
  solutions: string[];
  prevention: string[];
}

export const DEFICIENCY_GUIDES: DeficiencyGuide[] = [
  {
    element: 'Azote (N)',
    symptoms: [
      'Jaunissement généralisé des feuilles (en commençant par les plus anciennes)',
      'Croissance lente et chétive',
      'Feuillage clairsemé',
      'Plante de petite taille'
    ],
    causes: [
      'Sol pauvre en matière organique',
      'Lessivage par les pluies',
      'Compétition avec d\'autres plantes',
      'pH du sol inadéquat'
    ],
    solutions: [
      'Urée (46% N) : 20-30g/m² ou 3-4g/L en pulvérisation diluée',
      'Sang séché (12-14% N) : 50-75g/m²',
      'Corne broyée (13% N) : 50-75g/m² (libération lente)',
      'Purin d\'ortie : 10% pour arrosage, 5% pour pulvérisation'
    ],
    prevention: [
      'Apport régulier de compost',
      'Paillage pour limiter le lessivage',
      'Rotation des cultures',
      'Éviter la sur-fertilisation'
    ]
  },
  {
    element: 'Fer (Fe)',
    symptoms: [
      'Chlorose ferrique : jaunissement des jeunes feuilles avec nervures restant vertes',
      'Croissance ralentie',
      'Feuilles peuvent devenir blanches dans les cas sévères'
    ],
    causes: [
      'Sol calcaire (pH élevé)',
      'Excès d\'humidité',
      'Compaction du sol',
      'Excès de phosphore ou de zinc'
    ],
    solutions: [
      'Sulfate de fer : 2-3g/L en pulvérisation foliaire, 15-20g/m² au sol',
      'Chélate de fer pour sols très calcaires',
      'Acidification du sol avec soufre élémentaire'
    ],
    prevention: [
      'Améliorer le drainage',
      'Maintenir un pH approprié (6-7)',
      'Éviter les excès d\'arrosage',
      'Apport de matière organique'
    ]
  },
  {
    element: 'Magnésium (Mg)',
    symptoms: [
      'Jaunissement internervaire des feuilles anciennes (nervures restent vertes)',
      'Nécrose marginale (bords des feuilles)',
      'Mauvaise coloration des fruits',
      'Chute prématurée des feuilles'
    ],
    causes: [
      'Sol sableux (lessivage)',
      'Excès de potassium ou de calcium',
      'pH du sol inadéquat',
      'Stress hydrique'
    ],
    solutions: [
      'Sulfate de magnésium (Sel d\'Epsom) : 5g/L en pulvérisation, 20g/10L en arrosage',
      'Dolomie (carbonate de calcium et magnésium)',
      'Amendement organique riche en magnésium'
    ],
    prevention: [
      'Équilibrer les apports K-Ca-Mg',
      'Maintenir un pH optimal',
      'Arrosage régulier mais modéré',
      'Paillage pour conserver l\'humidité'
    ]
  },
  {
    element: 'Potassium (K)',
    symptoms: [
      'Brunissement et dessèchement des bords de feuilles',
      'Fruits de mauvaise qualité (petits, peu sucrés)',
      'Sensibilité accrue aux maladies et au froid',
      'Tiges faibles'
    ],
    causes: [
      'Sol sableux (lessivage)',
      'Excès d\'azote',
      'Sécheresse prolongée',
      'pH du sol inadéquat'
    ],
    solutions: [
      'Sulfate de potassium : 0.5-1g/L en arrosage, 20-40g/m² au sol',
      'Cendres de bois (riche en potasse)',
      'Compost bien décomposé'
    ],
    prevention: [
      'Apport régulier de matière organique',
      'Arrosage régulier en période sèche',
      'Éviter les excès d\'azote',
      'Maintenir un pH optimal'
    ]
  },
  {
    element: 'Phosphore (P)',
    symptoms: [
      'Coloration pourpre ou violacée des feuilles',
      'Retard de floraison et fructification',
      'Système racinaire peu développé',
      'Croissance générale ralentie'
    ],
    causes: [
      'Sol acide (pH < 6)',
      'Sol froid',
      'Excès d\'aluminium ou de fer',
      'Manque de matière organique'
    ],
    solutions: [
      'Phosphate naturel (poudre d\'os) : 50g/m²',
      'Engrais NPK équilibré',
      'Amendement calcaire si sol acide'
    ],
    prevention: [
      'Maintenir un pH optimal (6.5-7)',
      'Réchauffer le sol (paillage noir)',
      'Apport de compost',
      'Éviter le travail du sol par temps froid'
    ]
  },
  {
    element: 'Calcium (Ca)',
    symptoms: [
      'Nécrose apicale (cul noir des tomates)',
      'Déformation des fruits',
      'Brunissement des pointes de feuilles',
      'Mauvaise structure des tissus'
    ],
    causes: [
      'Sol acide',
      'Arrosage irrégulier',
      'Excès de potassium ou magnésium',
      'Mauvaise absorption (stress hydrique)'
    ],
    solutions: [
      'Amendement calcaire (coquilles d\'huîtres) : 100-500g/m²',
      'Chaux agricole',
      'Gypse (sulfate de calcium)'
    ],
    prevention: [
      'Arrosage régulier et constant',
      'Paillage pour maintenir l\'humidité',
      'Équilibrer les apports nutritifs',
      'Maintenir un pH optimal'
    ]
  },
  {
    element: 'Soufre (S)',
    symptoms: [
      'Jaunissement uniforme des jeunes feuilles (similaire à l\'azote mais sur jeunes feuilles)',
      'Croissance ralentie',
      'Feuilles petites et rigides',
      'Retard de maturité'
    ],
    causes: [
      'Sol pauvre en matière organique',
      'Lessivage en sol sableux',
      'pH du sol inadéquat',
      'Manque d\'apports organiques'
    ],
    solutions: [
      'Sulfate de magnésium : 20g/10L en arrosage (apporte Mg + S)',
      'Sulfate de potassium : 20-40g/m² (apporte K + S)',
      'Soufre élémentaire : 10-20g/m² (acidifie aussi le sol)',
      'Compost riche en matière organique'
    ],
    prevention: [
      'Apport régulier de compost',
      'Utilisation d\'engrais contenant du soufre',
      'Maintenir la matière organique du sol',
      'Éviter le lessivage excessif'
    ]
  },
  {
    element: 'Manganèse (Mn)',
    symptoms: [
      'Chlorose internervaire des jeunes feuilles (nervures vertes)',
      'Taches nécrotiques brunes entre les nervures',
      'Croissance ralentie',
      'Sensibilité accrue aux maladies'
    ],
    causes: [
      'Sol calcaire (pH > 7.5)',
      'Excès d\'humidité',
      'Excès de fer ou de zinc',
      'Sol mal drainé'
    ],
    solutions: [
      'Sulfate de manganèse : 1-2g/L en pulvérisation foliaire',
      'Chélate de manganèse pour sols calcaires',
      'Acidification du sol si pH trop élevé',
      'Amélioration du drainage'
    ],
    prevention: [
      'Maintenir un pH optimal (6.0-7.0)',
      'Améliorer le drainage',
      'Éviter les excès d\'arrosage',
      'Équilibrer les apports en oligo-éléments'
    ]
  },
  {
    element: 'Zinc (Zn)',
    symptoms: [
      'Chlorose internervaire des jeunes feuilles',
      'Feuilles petites et déformées',
      'Internœuds courts (nanisme)',
      'Brunissement des nervures'
    ],
    causes: [
      'Sol calcaire (pH > 7.0)',
      'Excès de phosphore',
      'Sol froid et humide',
      'Compaction du sol'
    ],
    solutions: [
      'Sulfate de zinc : 1-2g/L en pulvérisation foliaire',
      'Chélate de zinc pour sols calcaires',
      'Correction du pH si nécessaire',
      'Amélioration de la structure du sol'
    ],
    prevention: [
      'Maintenir un pH optimal',
      'Éviter les excès de phosphore',
      'Améliorer le drainage',
      'Apport de matière organique'
    ]
  },
  {
    element: 'Bore (B)',
    symptoms: [
      'Mort des bourgeons terminaux',
      'Déformation et épaississement des feuilles',
      'Fruits déformés et liégeux',
      'Fissures sur les tiges'
    ],
    causes: [
      'Sol sableux (lessivage)',
      'Sécheresse prolongée',
      'pH du sol inadéquat',
      'Excès de calcium'
    ],
    solutions: [
      'Borax : 0.5-1g/L en pulvérisation foliaire (ATTENTION: toxique à forte dose)',
      'Acide borique : 0.1-0.2g/L en pulvérisation',
      'Engrais contenant du bore',
      'Amélioration de la rétention d\'eau'
    ],
    prevention: [
      'Arrosage régulier',
      'Paillage pour conserver l\'humidité',
      'Éviter les excès de chaux',
      'Apport modéré et régulier'
    ]
  },
  {
    element: 'Cuivre (Cu)',
    symptoms: [
      'Chlorose des jeunes feuilles',
      'Feuilles flétries malgré un sol humide',
      'Brunissement des pointes de feuilles',
      'Croissance ralentie'
    ],
    causes: [
      'Sol sableux ou tourbeux',
      'pH du sol élevé',
      'Excès de zinc ou de fer',
      'Sol pauvre en matière organique'
    ],
    solutions: [
      'Sulfate de cuivre : 0.5-1g/L en pulvérisation foliaire',
      'Chélate de cuivre pour sols calcaires',
      'Bouillie bordelaise (préventif et curatif)',
      'Amendement organique'
    ],
    prevention: [
      'Apport de compost',
      'Maintenir un pH optimal',
      'Éviter les excès d\'autres oligo-éléments',
      'Rotation des cultures'
    ]
  },
  {
    element: 'Molybdène (Mo)',
    symptoms: [
      'Jaunissement des feuilles anciennes (similaire à l\'azote)',
      'Bords des feuilles brûlés',
      'Mauvaise nodulation des légumineuses',
      'Croissance ralentie'
    ],
    causes: [
      'Sol très acide (pH < 5.5)',
      'Excès de soufre',
      'Sol pauvre en matière organique',
      'Lessivage en sol sableux'
    ],
    solutions: [
      'Molybdate de sodium : 0.1-0.2g/L en pulvérisation foliaire',
      'Chaulage pour corriger l\'acidité',
      'Engrais contenant du molybdène',
      'Amendement calcaire'
    ],
    prevention: [
      'Maintenir un pH optimal (6.5-7.0)',
      'Apport de matière organique',
      'Éviter l\'acidification excessive',
      'Rotation avec légumineuses'
    ]
  }
];

// Fonction pour identifier une carence par ses symptômes
export const identifyDeficiency = (symptoms: string[]): DeficiencyGuide[] => {
  return DEFICIENCY_GUIDES.filter(guide =>
    guide.symptoms.some(symptom =>
      symptoms.some(userSymptom =>
        symptom.toLowerCase().includes(userSymptom.toLowerCase()) ||
        userSymptom.toLowerCase().includes(symptom.toLowerCase())
      )
    )
  );
};

// Contenants standards pour dosages
export const CONTAINER_SIZES = {
  pulverisateur_1L: 1,
  pulverisateur_5L: 5,
  arrosoir_11L: 11,
  arrosoir_13L: 13,
  pulverisateur_16L: 16
} as const;

// Fonction utilitaire pour convertir les dosages
export const convertDosage = (baseDosage: string, targetVolume: number): string => {
  // Exemple: "2-3g/L" -> pour 5L = "10-15g"
  const match = baseDosage.match(/(\d+(?:\.\d+)?)-?(\d+(?:\.\d+)?)?.*?g.*?\/.*?L/i);
  if (!match) return baseDosage;

  const minDose = parseFloat(match[1]);
  const maxDose = match[2] ? parseFloat(match[2]) : minDose;

  const minTotal = minDose * targetVolume;
  const maxTotal = maxDose * targetVolume;

  if (minDose === maxDose) {
    return `${minTotal}g`;
  } else {
    return `${minTotal}-${maxTotal}g`;
  }
};
