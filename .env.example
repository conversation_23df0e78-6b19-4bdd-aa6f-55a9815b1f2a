# Configuration Firebase
# Obtenez ces valeurs depuis votre console Firebase
VITE_FIREBASE_API_KEY=your_firebase_api_key_here
VITE_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id

# Configuration Google Gemini
# Obtenez votre clé API depuis Google AI Studio
VITE_GEMINI_API_KEY=your_gemini_api_key_here

# Configuration de l'environnement
NODE_ENV=development

# Configuration Email (choisir une option)

# Option 1: Gmail SMTP (pour développement)
GMAIL_USER=<EMAIL>
GMAIL_APP_PASSWORD=your_app_password

# Option 2: SendGrid
SENDGRID_API_KEY=your_sendgrid_api_key

# Option 3: SMTP générique
SMTP_HOST=smtp.your-provider.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=your_smtp_user
SMTP_PASSWORD=your_smtp_password

# Email expéditeur
FROM_EMAIL=<EMAIL>

# Instructions :
# 1. Copiez ce fichier vers .env.local
# 2. Remplacez les valeurs par vos vraies clés API
# 3. Ne commitez JAMAIS le fichier .env.local (il est dans .gitignore)
