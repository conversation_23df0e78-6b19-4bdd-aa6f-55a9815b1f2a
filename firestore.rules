rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Fonction utilitaire pour vérifier l'authentification
    function isAuthenticated() {
      return request.auth != null;
    }

    // Fonction pour vérifier la propriété d'un document
    function isOwner(userId) {
      return request.auth.uid == userId;
    }

    // Fonction pour vérifier les rôles admin
    function isAdmin() {
      return isAuthenticated() &&
             exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }

    // Fonction de validation des données utilisateur
    function validateUserData(data) {
      return data.keys().hasAll(['email', 'displayName']) &&
             data.email is string &&
             data.displayName is string &&
             data.email.matches('.*@.*\\..*');
    }

    // Règles pour les utilisateurs
    match /users/{userId} {
      // Lecture : utilisateur propriétaire ou admin
      allow read: if isOwner(userId) || isAdmin();

      // Écriture : utilisateur propriétaire avec validation des données
      allow create: if isOwner(userId) &&
                       validateUserData(request.resource.data);

      allow update: if isOwner(userId) &&
                       validateUserData(request.resource.data) &&
                       // Empêcher la modification du rôle par l'utilisateur
                       (!('role' in request.resource.data) ||
                        request.resource.data.role == resource.data.role);

      allow delete: if isOwner(userId) || isAdmin();

      // Sous-collections de l'utilisateur
      match /plants/{plantId} {
        allow read, write: if isOwner(userId);
        // Permettre les requêtes sur la collection plants
        allow list: if isOwner(userId);

        // Sous-collection des diagnostics (nom correct utilisé dans le code)
        match /diagnostics/{diagnosticId} {
          allow read, write: if isOwner(userId);
          allow list: if isOwner(userId);
        }

        // Sous-collection des diagnostic_records (pour compatibilité)
        match /diagnostic_records/{diagnosticId} {
          allow read, write: if isOwner(userId);
          allow list: if isOwner(userId);
        }
      }

      // Collection des événements de diagnostic
      match /diagnostic_events/{eventId} {
        allow read, write: if isOwner(userId);
        allow list: if isOwner(userId);
      }

      // Collection des notifications
      match /notifications/{notificationId} {
        allow read, write: if isOwner(userId);
        allow list: if isOwner(userId);
      }

      // Collection de l'historique des actions
      match /action_history/{actionId} {
        allow read, write: if isOwner(userId);
      }

      // Collection des paramètres Gemini
      match /gemini_settings/{settingId} {
        allow read, write: if isOwner(userId);
      }
    }

    // Règles pour les données publiques
    match /public/{document} {
      allow read: if true;
      allow write: if isAdmin();
    }

    // Collection des fertilisants (lecture seule pour tous les utilisateurs authentifiés)
    match /fertilizers/{fertilizerId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }

    // Collection des guides et documentation (lecture seule pour tous)
    match /guides/{guideId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }

    // Collection des archives (données archivées par année)
    match /archives/{archiveId} {
      // Vérifier que l'ID suit le format userId_year (ex: abc123_2024)
      allow read, write: if isAuthenticated() &&
                            archiveId.matches('.*_[0-9]{4}$') &&
                            archiveId.split('_')[0] == request.auth.uid;

      // Permettre les requêtes sur la collection avec filtre userId
      allow list: if isAuthenticated() &&
                     resource.data.userId == request.auth.uid;
    }

    // Règles par défaut (tout refuser)
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
